{"roots": ["link_hook"], "packages": [{"name": "link_hook", "version": "0.0.1", "dependencies": ["async", "collection", "crypto", "file", "glob", "logging", "meta", "native_assets_cli", "native_toolchain_c", "path", "pub_semver", "source_span", "string_scanner", "term_glyph", "typed_data", "yaml"], "devDependencies": ["_fe_analyzer_shared", "analyzer", "args", "boolean_selector", "cli_util", "convert", "coverage", "dart_style", "ffi", "ffigen", "flutter_lints", "frontend_server_client", "http_multi_server", "http_parser", "io", "js", "lints", "matcher", "mime", "node_preamble", "package_config", "pool", "quiver", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_map_stack_trace", "source_maps", "stack_trace", "stream_channel", "test", "test_api", "test_core", "vm_service", "watcher", "web", "web_socket", "web_socket_channel", "webkit_inspection_protocol", "yaml_edit"]}, {"name": "yaml_edit", "version": "2.2.2", "dependencies": ["collection", "meta", "source_span", "yaml"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "web_socket_channel", "version": "3.0.2", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "0.1.6", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.0.1", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "coverage", "version": "1.11.1", "dependencies": ["args", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "7.3.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "80.0.0", "dependencies": ["meta"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "ffigen", "version": "18.1.0", "dependencies": ["args", "cli_util", "collection", "dart_style", "ffi", "file", "glob", "logging", "meta", "package_config", "path", "pub_semver", "quiver", "yaml", "yaml_edit"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "native_toolchain_c", "version": "0.10.0", "dependencies": ["glob", "logging", "meta", "native_assets_cli", "pub_semver"]}, {"name": "native_assets_cli", "version": "0.13.0", "dependencies": ["collection", "crypto", "logging", "meta", "pub_semver", "yaml"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}], "configVersion": 1}