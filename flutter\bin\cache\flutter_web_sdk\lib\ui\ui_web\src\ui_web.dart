// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This library defines the web-specific additions that go along with dart:ui
//
// The web_sdk/sdk_rewriter.dart uses this directive.
// ignore: unnecessary_library_directive
library ui_web;

export 'ui_web/asset_manager.dart';
export 'ui_web/benchmarks.dart';
export 'ui_web/browser_detection.dart';
export 'ui_web/flutter_views_proxy.dart';
export 'ui_web/images.dart';
export 'ui_web/initialization.dart';
export 'ui_web/navigation/platform_location.dart';
export 'ui_web/navigation/url_strategy.dart';
export 'ui_web/platform_view_registry.dart';
export 'ui_web/plugins.dart';
export 'ui_web/testing.dart';
