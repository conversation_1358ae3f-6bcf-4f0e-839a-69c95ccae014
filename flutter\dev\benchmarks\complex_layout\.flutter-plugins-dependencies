{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\efhemni\\\\efhemni\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "android": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\efhemni\\\\efhemni\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "macos": [], "linux": [], "windows": [], "web": []}, "dependencyGraph": [{"name": "integration_test", "dependencies": []}], "date_created": "2025-08-08 02:45:32.016167", "version": "3.32.8", "swift_package_manager_enabled": {"ios": false, "macos": false}}