{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\efhemni\\\\efhemni\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\efhemni\\\\efhemni\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.15\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_android-2.8.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.4.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "video_player_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_web-2.3.4\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "integration_test", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}], "date_created": "2025-08-08 02:52:45.514188", "version": "3.32.8", "swift_package_manager_enabled": {"ios": false, "macos": false}}